"""
自动字段映射生成器

功能说明:
- 在数据导入时自动为每个表生成完整的字段映射配置
- 智能映射算法 + 用户自定义规则
- 多策略映射生成：精确匹配、模糊匹配、保持原始名称
- 映射质量评估和建议生成

功能函数:
- generate_mapping(): 生成字段映射配置
- assess_mapping_quality(): 评估映射质量
- update_mapping_rules(): 更新映射规则
- export_mapping_template(): 导出映射模板

创建时间: 2025-06-23
作者: 开发团队
"""

import re
import json
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime
from difflib import SequenceMatcher

from src.utils.log_config import setup_logger


class AutoFieldMappingGenerator:
    """自动字段映射生成器
    
    提供智能的字段映射自动生成功能
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化自动映射生成器
        
        Args:
            config_path: 配置文件路径
        """
        self.logger = setup_logger(__name__)
        
        # 初始化配置
        self.config_path = config_path or "state/data/field_mappings.json"
        self.mapping_rules_path = "state/data/auto_mapping_rules.json"
        
        # 加载映射规则
        self.standard_mappings = self._load_standard_mappings()
        self.user_mapping_rules = self._load_user_mapping_rules()
        
        # 映射质量统计
        self.mapping_stats = {
            "total_generated": 0,
            "exact_matches": 0,
            "fuzzy_matches": 0,
            "original_kept": 0
        }
        
        self.logger.info("自动字段映射生成器初始化完成")
    
    def generate_mapping(self, excel_columns: List[str], table_name: str) -> Dict[str, str]:
        """
        多策略字段映射生成算法
        
        策略优先级:
        1. 精确匹配已知字段映射规则
        2. 模糊匹配常用字段模式
        3. 保持原始列名作为显示名
        4. 用户自定义映射规则
        
        Args:
            excel_columns: Excel列名列表
            table_name: 表名
            
        Returns:
            Dict[str, str]: 生成的字段映射配置
        """
        mapping_result = {}
        generation_log = []
        
        self.logger.info(f"开始为表 '{table_name}' 生成字段映射，共 {len(excel_columns)} 个字段")
        
        for excel_col in excel_columns:
            # 清理字段名
            clean_col = self._clean_field_name(excel_col)
            
            # 策略1: 精确匹配
            exact_match = self._exact_match_mapping(clean_col)
            if exact_match:
                mapping_result[excel_col] = exact_match
                generation_log.append(f"精确匹配: {excel_col} -> {exact_match}")
                self.mapping_stats["exact_matches"] += 1
                continue
            
            # 策略2: 模糊匹配
            fuzzy_match = self._fuzzy_match_mapping(clean_col)
            if fuzzy_match:
                mapping_result[excel_col] = fuzzy_match
                generation_log.append(f"模糊匹配: {excel_col} -> {fuzzy_match}")
                self.mapping_stats["fuzzy_matches"] += 1
                continue
            
            # 策略3: 用户自定义规则匹配
            user_match = self._user_rule_mapping(clean_col, table_name)
            if user_match:
                mapping_result[excel_col] = user_match
                generation_log.append(f"用户规则匹配: {excel_col} -> {user_match}")
                continue
            
            # 策略4: 中文表头特殊处理
            if self._contains_chinese(excel_col):
                # 如果是中文表头，直接使用原始表头作为显示名
                mapping_result[excel_col] = excel_col
                generation_log.append(f"中文表头保持: {excel_col} -> {excel_col}")
                self.mapping_stats["original_kept"] += 1
            else:
                # 策略5: 保持原始名称
                mapping_result[excel_col] = clean_col
                generation_log.append(f"保持原名: {excel_col} -> {clean_col}")
                self.mapping_stats["original_kept"] += 1
        
        self.mapping_stats["total_generated"] += len(excel_columns)
        
        # 记录生成日志
        self.logger.info(f"字段映射生成完成，详细信息:")
        for log_entry in generation_log:
            self.logger.debug(log_entry)
        
        return mapping_result
    
    def _clean_field_name(self, field_name: str) -> str:
        """清理字段名称
        
        Args:
            field_name: 原始字段名
            
        Returns:
            str: 清理后的字段名
        """
        if not field_name:
            return ""
        
        # 去除首尾空白
        cleaned = str(field_name).strip()
        
        # 移除多余的空白字符
        cleaned = re.sub(r'\s+', '', cleaned)
        
        # 移除特殊字符
        cleaned = re.sub(r'[^\w\u4e00-\u9fff]', '', cleaned)
        
        return cleaned
    
    def _exact_match_mapping(self, field_name: str) -> Optional[str]:
        """精确匹配映射
        
        Args:
            field_name: 字段名
            
        Returns:
            Optional[str]: 匹配的映射名，如果没有匹配返回None
        """
        # 检查标准映射中的精确匹配
        for pattern_info in self.standard_mappings.values():
            if isinstance(pattern_info, dict):
                # 检查aliases中的精确匹配
                aliases = pattern_info.get("aliases", [])
                if field_name in aliases or field_name.lower() in [alias.lower() for alias in aliases]:
                    return pattern_info.get("display_name", field_name)
                
                # 检查正则表达式精确匹配
                regex_pattern = pattern_info.get("regex", "")
                if regex_pattern and re.fullmatch(regex_pattern, field_name, re.IGNORECASE):
                    return pattern_info.get("display_name", field_name)
        
        return None
    
    def _fuzzy_match_mapping(self, field_name: str) -> Optional[str]:
        """模糊匹配映射
        
        Args:
            field_name: 字段名
            
        Returns:
            Optional[str]: 匹配的映射名，如果没有匹配返回None
        """
        best_match = None
        best_score = 0.0
        min_similarity = 0.7  # 最小相似度阈值
        
        for pattern_info in self.standard_mappings.values():
            if isinstance(pattern_info, dict):
                # 与display_name的相似度匹配
                display_name = pattern_info.get("display_name", "")
                similarity = self._calculate_similarity(field_name, display_name)
                
                if similarity > best_score and similarity >= min_similarity:
                    best_score = similarity
                    best_match = display_name
                
                # 与aliases的相似度匹配
                aliases = pattern_info.get("aliases", [])
                for alias in aliases:
                    similarity = self._calculate_similarity(field_name, alias)
                    if similarity > best_score and similarity >= min_similarity:
                        best_score = similarity
                        best_match = display_name
        
        return best_match
    
    def _user_rule_mapping(self, field_name: str, table_name: str) -> Optional[str]:
        """用户自定义规则映射
        
        Args:
            field_name: 字段名
            table_name: 表名
            
        Returns:
            Optional[str]: 匹配的映射名，如果没有匹配返回None
        """
        # 检查全局用户规则
        global_patterns = self.user_mapping_rules.get("global_patterns", {})
        if field_name in global_patterns:
            return global_patterns[field_name]
        
        # 检查表特定规则
        table_patterns = self.user_mapping_rules.get("table_specific_patterns", {})
        for pattern, mappings in table_patterns.items():
            # 支持通配符匹配
            if self._match_table_pattern(table_name, pattern):
                if field_name in mappings:
                    return mappings[field_name]
        
        return None
    
    def _match_table_pattern(self, table_name: str, pattern: str) -> bool:
        """匹配表名模式（支持通配符）
        
        Args:
            table_name: 表名
            pattern: 模式（支持*通配符）
            
        Returns:
            bool: 是否匹配
        """
        # 转换通配符为正则表达式
        regex_pattern = pattern.replace("*", ".*")
        return bool(re.match(regex_pattern, table_name, re.IGNORECASE))
    
    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """计算两个字符串的相似度
        
        Args:
            str1: 字符串1
            str2: 字符串2
            
        Returns:
            float: 相似度分数 (0.0-1.0)
        """
        if not str1 or not str2:
            return 0.0
        
        return SequenceMatcher(None, str1.lower(), str2.lower()).ratio()
    
    def _load_standard_mappings(self) -> Dict[str, Any]:
        """加载标准映射规则库
        
        Returns:
            Dict[str, Any]: 标准映射规则
        """
        return {
            # 基础信息字段
            "employee_id": {
                "aliases": ["工号", "员工号", "职工编号", "编号"],
                "display_name": "工号"
            },
            "employee_name": {
                "aliases": ["姓名", "员工姓名", "职工姓名", "名字"],
                "display_name": "姓名"
            },
            "department": {
                "aliases": ["部门", "所属部门", "单位", "科室", "部门名称"],
                "display_name": "部门"
            },
            "position": {
                "aliases": ["职位", "岗位", "职务"],
                "display_name": "职位"
            },
            
            # 工资字段
            "basic_salary": {
                "aliases": ["基本工资", "基础工资", "基本薪资"],
                "display_name": "基本工资"
            },
            "position_salary": {
                "aliases": ["岗位工资", "职位工资", "岗位薪资"],
                "display_name": "岗位工资"
            },
            "grade_salary": {
                "aliases": ["薪级工资", "等级工资", "薪级薪资"],
                "display_name": "薪级工资"
            },
            "performance": {
                "aliases": ["绩效", "奖金", "绩效工资", "绩效奖金"],
                "display_name": "绩效工资"
            },
            "allowance": {
                "aliases": ["津贴", "补贴", "津贴补贴"],
                "display_name": "津贴补贴"
            },
            "total_salary": {
                "aliases": ["应发工资", "应发合计", "工资合计", "总工资"],
                "display_name": "应发合计"
            },
            "social_insurance": {
                "aliases": ["五险一金", "社保", "公积金"],
                "display_name": "五险一金个人"
            }
        }
    
    def _load_user_mapping_rules(self) -> Dict[str, Any]:
        """加载用户自定义映射规则
        
        Returns:
            Dict[str, Any]: 用户映射规则
        """
        try:
            if Path(self.mapping_rules_path).exists():
                with open(self.mapping_rules_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"加载用户映射规则失败: {e}")
        
        # 返回默认用户规则
        return {
            "global_patterns": {
                "薪级工资": "薪级工资",
                "岗位工资": "岗位工资",
                "应发工资": "应发合计"
            },
            "table_specific_patterns": {
                "salary_data_*": {
                    "2025年岗位工资": "岗位工资",
                    "2025年薪级工资": "薪级工资",
                    "2025年奖励性绩效预发": "绩效工资"
                }
            }
        }
    
    def assess_mapping_quality(self, mapping: Dict[str, str]) -> Dict[str, Any]:
        """评估生成的映射质量
        
        Args:
            mapping: 字段映射配置
            
        Returns:
            Dict[str, Any]: 质量评估结果
        """
        total_fields = len(mapping)
        if total_fields == 0:
            return {"coverage_rate": 0, "overall_score": 0}
        
        # 计算覆盖率
        chinese_mapped = sum(1 for v in mapping.values() if self._contains_chinese(v))
        coverage_rate = chinese_mapped / total_fields
        
        return {
            "coverage_rate": round(coverage_rate, 3),
            "total_fields": total_fields,
            "chinese_mapped": chinese_mapped,
            "overall_score": round(coverage_rate, 3)
        }
    
    def _contains_chinese(self, text: str) -> bool:
        """检查文本是否包含中文字符
        
        Args:
            text: 文本
            
        Returns:
            bool: 是否包含中文
        """
        return bool(re.search(r'[\u4e00-\u9fff]', text))
    
    def get_mapping_statistics(self) -> Dict[str, Any]:
        """获取映射生成统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return self.mapping_stats.copy() 