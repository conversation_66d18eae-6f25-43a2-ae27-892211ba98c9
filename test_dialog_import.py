#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试HeaderCustomizationDialog导入和创建
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir
src_dir = project_root / "src"
sys.path.insert(0, str(src_dir))
sys.path.insert(0, str(project_root))

def test_dialog_import():
    """测试对话框导入"""
    try:
        print("测试HeaderCustomizationDialog导入...")
        
        # 测试导入
        from src.gui.dialogs import HeaderCustomizationDialog
        print("✅ HeaderCustomizationDialog导入成功")
        
        # 测试创建（不显示）
        from PyQt5.QtWidgets import QApplication
        
        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 测试数据
        all_fields = {
            'employee_id': '工号',
            'employee_name': '姓名', 
            'department': '部门',
            'basic_salary': '基本工资'
        }
        selected_fields = ['employee_id', 'employee_name']
        
        # 创建对话框实例
        dialog = HeaderCustomizationDialog(
            all_fields=all_fields,
            selected_fields=selected_fields
        )
        print("✅ HeaderCustomizationDialog创建成功")
        
        # 测试获取选择的字段
        current_selected = dialog.get_selected_fields()
        print(f"✅ 当前选择的字段: {current_selected}")
        
        print("🎉 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_dialog_import()
    if success:
        print("\n对话框功能正常，可以在主系统中使用。")
    else:
        print("\n对话框功能异常，需要进一步修复。")
