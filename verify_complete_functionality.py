#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完整功能验证脚本

验证表头映射与自定义功能的所有组件是否正常工作
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir
src_dir = project_root / "src"
sys.path.insert(0, str(src_dir))
sys.path.insert(0, str(project_root))

def test_all_components():
    """测试所有组件"""
    print("=" * 60)
    print("完整功能验证测试")
    print("=" * 60)
    
    results = {}
    
    # 1. 测试核心模块导入
    print("\n1. 测试核心模块导入...")
    try:
        from src.modules.data_import.config_sync_manager import ConfigSyncManager
        from src.modules.data_import.auto_field_mapping_generator import AutoFieldMappingGenerator
        from src.gui.dialogs import HeaderCustomizationDialog
        print("✅ 所有核心模块导入成功")
        results["模块导入"] = True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        results["模块导入"] = False
        return results
    
    # 2. 测试字段映射功能
    print("\n2. 测试字段映射功能...")
    try:
        auto_mapping = AutoFieldMappingGenerator()
        test_columns = ['工号', '姓名', '部门名称', '2026年岗位工资', '应发工资']
        mapping = auto_mapping.generate_mapping(test_columns, "test_table_2026_11")
        
        if mapping and len(mapping) == len(test_columns):
            print(f"✅ 字段映射生成成功: {mapping}")
            results["字段映射"] = True
        else:
            print("❌ 字段映射生成失败")
            results["字段映射"] = False
    except Exception as e:
        print(f"❌ 字段映射测试失败: {e}")
        results["字段映射"] = False
    
    # 3. 测试配置管理功能
    print("\n3. 测试配置管理功能...")
    try:
        config_sync = ConfigSyncManager()
        
        # 测试映射保存和加载
        test_mapping = {'工号': '工号', '姓名': '姓名', '部门名称': '部门'}
        save_success = config_sync.save_mapping("test_table_2026_11", test_mapping)
        loaded_mapping = config_sync.load_mapping("test_table_2026_11")
        
        # 测试用户偏好保存和加载
        test_fields = ['工号', '姓名']
        pref_success = config_sync.save_user_header_preference("test_table_2026_11", test_fields)
        loaded_fields = config_sync.get_user_header_preference("test_table_2026_11")
        
        if (save_success and loaded_mapping == test_mapping and 
            pref_success and loaded_fields == test_fields):
            print("✅ 配置管理功能正常")
            results["配置管理"] = True
        else:
            print("❌ 配置管理功能异常")
            results["配置管理"] = False
    except Exception as e:
        print(f"❌ 配置管理测试失败: {e}")
        results["配置管理"] = False
    
    # 4. 测试对话框功能
    print("\n4. 测试对话框功能...")
    try:
        from PyQt5.QtWidgets import QApplication
        
        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建对话框
        all_fields = {
            'employee_id': '工号',
            'employee_name': '姓名',
            'department': '部门',
            'basic_salary': '基本工资',
            'total_salary': '应发合计'
        }
        selected_fields = ['employee_id', 'employee_name', 'total_salary']
        
        dialog = HeaderCustomizationDialog(
            all_fields=all_fields,
            selected_fields=selected_fields
        )
        
        # 测试获取选择的字段
        current_selected = dialog.get_selected_fields()
        
        if set(current_selected) == set(selected_fields):
            print("✅ 对话框功能正常")
            results["对话框"] = True
        else:
            print("❌ 对话框功能异常")
            results["对话框"] = False
    except Exception as e:
        print(f"❌ 对话框测试失败: {e}")
        results["对话框"] = False
    
    # 5. 测试主窗口集成
    print("\n5. 测试主窗口集成...")
    try:
        # 检查主窗口文件中的关键方法
        main_window_file = project_root / "src" / "gui" / "prototype" / "prototype_main_window.py"
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_methods = [
            "_show_header_customization_dialog",
            "_reset_headers_to_default", 
            "_get_all_available_fields",
            "_save_user_header_preference",
            "_reload_current_table_data"
        ]
        
        missing_methods = []
        for method in required_methods:
            if method not in content:
                missing_methods.append(method)
        
        if not missing_methods:
            print("✅ 主窗口集成完整")
            results["主窗口集成"] = True
        else:
            print(f"❌ 主窗口集成缺失方法: {missing_methods}")
            results["主窗口集成"] = False
    except Exception as e:
        print(f"❌ 主窗口集成测试失败: {e}")
        results["主窗口集成"] = False
    
    return results

def generate_final_report(results):
    """生成最终报告"""
    print("\n" + "=" * 60)
    print("最终验证报告")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"总测试项: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {(passed_tests / total_tests * 100):.1f}%")
    
    print("\n详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有功能验证通过！")
        print("\n✨ 表头映射与自定义功能已完全实现并可正常使用")
        print("\n📖 使用指南:")
        print("1. 启动系统: python main.py")
        print("2. 点击右上角设置按钮（⚙️）显示菜单栏")
        print("3. 选择数据表后，使用'数据' → '自定义表头'功能")
        print("4. 在对话框中选择要显示的字段")
        print("5. 使用'数据' → '重置表头'恢复默认显示")
        
        print("\n🔧 功能特性:")
        print("- 智能字段映射：自动生成中文显示名")
        print("- 用户自定义：可选择显示的字段")
        print("- 按需加载：只加载选择的字段，提升性能")
        print("- 持久化配置：用户设置自动保存")
        print("- 分页支持：大数据量下仍能快速响应")
    else:
        print("\n⚠️ 部分功能验证失败")
        print("请检查失败的项目并重新测试")

def main():
    """主函数"""
    results = test_all_components()
    generate_final_report(results)

if __name__ == "__main__":
    main()
